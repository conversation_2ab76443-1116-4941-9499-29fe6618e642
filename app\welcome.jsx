import { StyleSheet, View, Image, Platform } from 'react-native'
import { router } from 'expo-router'
import { Colors } from '../constants/Colors'
import ThemedView from '../components/ThemedView'
import ThemedText from '../components/ThemedText'
import Spacer from '../components/Spacer'
import GradientGlassButton from '../components/GradientGlassButton'
import WebCompatibleView from '../components/WebCompatibleView'
import { getRecommendations } from '../lib/api'
import { useRecommendation } from '../contexts/UserContext'

const Welcome = () => {
  const handleLogin = () => {
    router.push('/(auth)/login')
  }

  const { setRecommendations } = useRecommendation();
  const handleGuest = async () => {
    try {
      const data = await getRecommendations(); // 自动判断是否游客
      setRecommendations(data); // 存入全局推荐状态
      router.replace('/(dashboard)/plans?tab=plans');
    } catch (err) {
      console.error('推荐内容加载失败:', err.message);
    }
  };

  return (
    <WebCompatibleView
      style={styles.container}
      fallbackStyle={styles.fallbackStyle}
    >
      <ThemedView style={styles.innerContainer} safe={true}>
        <Spacer height={Platform.OS === 'web' ? 80 : 100} />

        {/* 卡通形象 */}
        <View style={styles.characterContainer}>
          <Image
            source={require('../assets/standdog.png')}
            style={[styles.characterImage, { resizeMode: 'contain' }]}
          />
        </View>

        <Spacer height={Platform.OS === 'web' ? 40 : 60} />

        {/* APP名称 */}
        <ThemedText style={styles.appName}>
          APP名称
        </ThemedText>

        <Spacer height={Platform.OS === 'web' ? 80 : 120} />

        {/* 登录按钮 */}
        <GradientGlassButton
          title="登录"
          onPress={handleLogin}
          style={styles.loginButton}
        />

        <Spacer height={20} />

        {/* 游客按钮 */}
        <GradientGlassButton
          title="游客"
          onPress={handleGuest}
          style={styles.guestButton}
          gradientColors={['#FFE7CE', '#FDAA6C']}
          borderColor="rgba(255, 231, 206, 0.5)"
          blurBackgroundColor="rgba(255, 231, 206, 0.3)"
        />

        <Spacer height={60} />
      </ThemedView>
    </WebCompatibleView>
  )
}

export default Welcome

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  fallbackStyle: {
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  innerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Platform.OS === 'web' ? 40 : 20,
  },
  characterContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  characterImage: {
    width: Platform.OS === 'web' ? 180 : 200,
    height: Platform.OS === 'web' ? 180 : 200,
  },
  appName: {
    fontSize: Platform.OS === 'web' ? 28 : 32,
    fontWeight: 'bold',
    color: Colors.boldText,
    textAlign: 'center',
  },
  loginButton: {
    width: '80%',
    maxWidth: Platform.OS === 'web' ? 350 : 300,
    marginTop: 0,
    marginBottom: 0,
  },
  guestButton: {
    width: '80%',
    maxWidth: Platform.OS === 'web' ? 350 : 300,
    marginTop: 0,
    marginBottom: 0,
  },
})
