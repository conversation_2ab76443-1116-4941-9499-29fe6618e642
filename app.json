{"expo": {"scheme": "<PERSON><PERSON>ap<PERSON>", "name": "my-react-native-app", "slug": "my-react-native-app", "version": "1.0.0", "sdkVersion": "53.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router"]}}