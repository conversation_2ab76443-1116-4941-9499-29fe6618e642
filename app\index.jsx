import { StyleSheet } from 'react-native'
import { router } from 'expo-router'
import { useEffect } from 'react'
import { useUser } from '../hooks/useUser'

import ThemedView from "../components/ThemedView"
import ThemedText from "../components/ThemedText"

const Home = () => {
  const { user, authChecked } = useUser();

  useEffect(() => {
    if (authChecked) {
      if (user) {
        // 已登录用户直接进入dashboard
        router.replace('/(dashboard)/plans?tab=plans');
      } else {
        // 未登录用户进入开屏页面
        router.replace('/welcome');
      }
    }
  }, [user, authChecked]);

  // 如果还在检查认证状态，显示加载
  if (!authChecked) {
    return (
      <ThemedView style={styles.container}>
        <ThemedText>Loading...</ThemedText>
      </ThemedView>
    );
  }

  // 这个页面不应该被显示，因为会自动重定向
  return (
    <ThemedView style={styles.container}>
      <ThemedText>Redirecting...</ThemedText>
    </ThemedView>
  )
}

export default Home

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  img: {
    marginVertical: 20
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
  },
  link: {
    marginVertical: 10,
    borderBottomWidth: 1
  },
})