// src/services/api.js
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

// 网络检测
const checkNetwork = async () => {
  const state = await NetInfo.fetch();
  if (!state.isConnected) {
    throw new Error('网络不可用，请检查连接');
  }
};

const API_BASE_URL = __DEV__
  ? 'https://dev-api.example.com'
  : 'https://prod-api.example.com';

// ✅ 通用请求封装，加入 token
const request = async (endpoint, method = 'GET', data = null) => {
  await checkNetwork();
  const url = `${API_BASE_URL}/${endpoint}`;

  const token = await AsyncStorage.getItem('token'); // ✅ 获取 token

  const headers = {
    'Content-Type': 'application/json',
    ...(token && { Authorization: `Bearer ${token}` }), // ✅ 加入 token 头
  };

  const config = {
    method,
    headers,
    ...(data && { body: JSON.stringify(data) }),
  };

  try {
    const response = await fetch(url, config); // ✅ 你没有定义 fetchWithAuth，直接用 fetch 即可
    const result = await response.json();

    if (!response.ok) {
      throw new Error(result.message || '请求失败');
    }

    return result;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};
