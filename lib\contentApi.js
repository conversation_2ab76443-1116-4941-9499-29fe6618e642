import { fetchWithAuth } from './fetchWithAuth';
import { API_BASE_URL } from '../constants/index';

// 统一的数据获取接口
export const getIndexData = async (userId = 1) => {
  const response = await fetchWithAuth(`${API_BASE_URL}/index?userId=${userId}`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const result = await response.json();

  console.log('contentApi原始返回数据:', result);

  // 处理标准的API返回结构：{ code: 0, message: "ok", data: {...} }
  if (result.code === 0 && result.data) {
    console.log('contentApi解析数据:', result.data);
    return { data: result.data }; // 返回标准格式
  } else if (result.data) {
    // 兼容直接返回 data 的情况
    return { data: result.data };
  } else {
    // 兼容直接返回内容的情况
    return { data: result };
  }
};

// 为了保持兼容性，保留原有的函数名，但都调用统一接口
export const getFeaturedContentList = async () => {
  const response = await getIndexData();
  const data = response.data || {};

  // 优先从新结构获取，兼容旧结构
  const featuredData = data.featured || data.featuredContentList || [];
  return { data: featuredData };
};

export const getHotCourseList = async () => {
  const response = await getIndexData();
  const data = response.data || {};

  // 优先从新结构获取，兼容旧结构
  const hotData = data.hot || data.hotCourseList || [];
  return { data: hotData };
};

export const getLatestCourseList = async () => {
  const response = await getIndexData();
  const data = response.data || {};

  // 优先从新结构获取，兼容旧结构
  const latestData = data.latest || data.latestCourseList || [];
  return { data: latestData };
};

export const getRecommendVideoList = async () => {
  const response = await getIndexData();
  const data = response.data || {};

  // 优先使用兴趣推荐，如果没有则使用画像推荐，最后兼容旧结构
  const interestRecommend = data.interestRecommend || [];
  const profileRecommend = data.profileRecommend || [];
  const oldRecommendData = data.recommendVideoList || [];

  const recommendData = interestRecommend.length > 0 ? interestRecommend :
    profileRecommend.length > 0 ? profileRecommend :
      oldRecommendData;

  return { data: recommendData };
};
