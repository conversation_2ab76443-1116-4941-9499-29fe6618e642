import { fetchWithAuth } from './fetchWithAuth';
import { API_BASE_URL } from '../constants/index';

// 统一的数据获取接口
export const getIndexData = async (userId = 1) => {
  const response = await fetchWithAuth(`${API_BASE_URL}/index?userId=${userId}`);
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  const data = await response.json();
  return { data };
};

// 为了保持兼容性，保留原有的函数名，但都调用统一接口
export const getFeaturedContentList = async () => {
  const response = await getIndexData();
  // 从所有数据中筛选精品内容（isFeatured = true）
  const allData = response.data || [];
  const featuredData = allData.filter(item => item.isFeatured === true);
  return { data: featuredData };
};

export const getHotCourseList = async () => {
  const response = await getIndexData();
  // 从所有数据中按播放量排序，取前几个作为热门课程
  const allData = response.data || [];
  const hotData = allData
    .sort((a, b) => (b.playCount || 0) - (a.playCount || 0))
    .slice(0, 10); // 取前10个
  return { data: hotData };
};

export const getLatestCourseList = async () => {
  const response = await getIndexData();
  // 从所有数据中按创建时间排序，取最新的几个
  const allData = response.data || [];
  const latestData = allData
    .sort((a, b) => new Date(b.createTime || 0) - new Date(a.createTime || 0))
    .slice(0, 10); // 取前10个
  return { data: latestData };
};

export const getRecommendVideoList = async (userId) => {
  const response = await getIndexData(userId);
  // 返回所有数据作为推荐视频，或者可以根据用户ID做个性化推荐
  const allData = response.data || [];
  return { data: allData };
};
