// api/userProfile.js
import { API_BASE_URL } from '../constants/index';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchWithAuth } from './fetchWithAuth';

// 保存用户标签并生成 AI 画像
export async function saveUserTagAndGenerateProfile(selections) {
  try {
    // 将 selections 对象转换为 tagIdList 数组
    const tagIdList = Object.values(selections).flat();

    console.log('saveUserTagAndGenerateProfile - selections:', selections);
    console.log('saveUserTagAndGenerateProfile - tagIdList:', tagIdList);

    const requestBody = {
      tagIdList, // 使用正确的字段名 tagIdList
    };

    console.log('saveUserTagAndGenerateProfile - requestBody:', requestBody);

    const response = await fetchWithAuth(`${API_BASE_URL}/profile/save`, {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    const result = await response.json();

    if (!response.ok || result.code !== 0) {
      throw new Error(result.message || '保存失败');
    }

    return result.data; // 即 UserProfileAiResponse
  } catch (error) {
    console.error('API 错误:', error);
    throw error;
  }
}

// 发送选中id到后端
export async function sendSelectedIdsToBackend(ids) {
  try {
    console.log('sendSelectedIdsToBackend - ids:', ids);

    const requestBody = {
      tagIdList: ids // 使用正确的字段名 tagIdList
    };

    console.log('sendSelectedIdsToBackend - requestBody:', requestBody);

    // 使用fetchWithAuth添加token认证
    const response = await fetchWithAuth(`${API_BASE_URL}/sav`, {
      method: 'POST',
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      throw new Error('网络请求失败');
    }

    return await response.json();
  } catch (error) {
    console.error('发送选中ID到后端失败:', error);
    // 可以根据需要决定是否抛出或静默
  }
}
