import AsyncStorage from '@react-native-async-storage/async-storage';

// 与api.js保持一致的存储键名
const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
};

export const fetchWithAuth = async (url, options = {}) => {
  try {
    const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);

    const headers = {
      ...options.headers,
      "Content-Type": "application/json",
    };

    // 只有在token存在时才添加Authorization头
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    // 如果返回401未授权，可能需要重新登录
    if (response.status === 401) {
      console.warn('Token可能已过期，需要重新登录');
      // 可以在这里添加自动登出逻辑
      // await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    }

    return response;
  } catch (error) {
    console.error('fetchWithAuth错误:', error);
    throw error;
  }
};
